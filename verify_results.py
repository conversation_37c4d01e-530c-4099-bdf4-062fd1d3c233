#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证数据处理结果
"""

import pandas as pd
import numpy as np

def verify_processing_results():
    """验证处理结果"""
    
    # 读取原始数据和处理后数据
    original_file = "boss3合同签约单位清单20250722.xlsx"
    processed_file = "boss3合同签约单位清单20250722_processed_20250723_184659.xlsx"
    
    print("=== 验证数据处理结果 ===")
    
    # 读取数据
    df_original = pd.read_excel(original_file, header=1)
    df_processed = pd.read_excel(processed_file)
    
    print(f"原始数据: {len(df_original)} 行")
    print(f"处理后数据: {len(df_processed)} 行")
    
    # 验证合同编号唯一性
    original_contracts = set(df_original['合同编号'].unique())
    processed_contracts = set(df_processed['合同编号'].unique())
    
    print(f"原始唯一合同数: {len(original_contracts)}")
    print(f"处理后唯一合同数: {len(processed_contracts)}")
    print(f"合同编号是否一致: {original_contracts == processed_contracts}")
    
    # 检查几个有多个签约单位的合同
    print("\n=== 示例检查 ===")
    
    # 找一个有重复的合同编号
    contract_counts = df_original['合同编号'].value_counts()
    sample_contract = contract_counts.index[0]  # 重复最多的合同
    
    print(f"示例合同编号: {sample_contract}")
    print(f"原始数据中该合同的记录数: {contract_counts[sample_contract]}")
    
    # 原始数据中的记录
    original_records = df_original[df_original['合同编号'] == sample_contract]
    print("\n原始记录中的签约单位:")
    for idx, row in original_records.iterrows():
        unit = row['合同中的签约单位']
        org_type = row['签约单位的机构类型']
        print(f"  - {unit} ({org_type})")
    
    # 处理后的记录
    processed_record = df_processed[df_processed['合同编号'] == sample_contract]
    if len(processed_record) > 0:
        merged_units = processed_record.iloc[0]['合同中的签约单位']
        merged_types = processed_record.iloc[0]['签约单位的机构类型']
        print(f"\n合并后的签约单位: {merged_units}")
        print(f"合并后的机构类型: {merged_types}")
    
    # 检查数据完整性
    print("\n=== 数据完整性检查 ===")
    
    # 检查是否有合同编号丢失
    missing_contracts = original_contracts - processed_contracts
    extra_contracts = processed_contracts - original_contracts
    
    if missing_contracts:
        print(f"丢失的合同编号: {missing_contracts}")
    else:
        print("✓ 没有合同编号丢失")
    
    if extra_contracts:
        print(f"多出的合同编号: {extra_contracts}")
    else:
        print("✓ 没有多余的合同编号")
    
    # 检查处理后数据的字段完整性
    print(f"\n原始数据字段: {list(df_original.columns)}")
    print(f"处理后数据字段: {list(df_processed.columns)}")
    print(f"字段是否一致: {list(df_original.columns) == list(df_processed.columns)}")
    
    # 统计合并情况
    print("\n=== 合并统计 ===")
    
    # 统计包含多个签约单位的记录
    multi_unit_count = 0
    max_units = 0
    
    for idx, row in df_processed.iterrows():
        units = str(row['合同中的签约单位'])
        if ';' in units:
            multi_unit_count += 1
            unit_count = len(units.split(';'))
            max_units = max(max_units, unit_count)
    
    print(f"包含多个签约单位的合同数: {multi_unit_count}")
    print(f"单个合同最多签约单位数: {max_units}")
    print(f"平均每个合同的原始记录数: {len(df_original) / len(df_processed):.2f}")
    
    print("\n=== 验证完成 ===")

if __name__ == "__main__":
    verify_processing_results()
