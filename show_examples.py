#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
展示数据处理前后的对比示例
"""

import pandas as pd

def show_processing_examples():
    """展示处理前后的对比示例"""
    
    # 读取数据
    df_original = pd.read_excel("boss3合同签约单位清单20250722.xlsx", header=1)
    df_processed = pd.read_excel("boss3合同签约单位清单20250722_processed_20250723_184659.xlsx")
    
    print("=== 数据处理前后对比示例 ===\n")
    
    # 选择几个有代表性的合同编号
    contract_counts = df_original['合同编号'].value_counts()
    sample_contracts = contract_counts.head(3).index.tolist()
    
    for i, contract_id in enumerate(sample_contracts, 1):
        print(f"示例 {i}: 合同编号 {contract_id}")
        print("=" * 50)
        
        # 原始数据
        original_records = df_original[df_original['合同编号'] == contract_id]
        print(f"原始数据 ({len(original_records)} 条记录):")
        
        for idx, row in original_records.iterrows():
            print(f"  记录 {idx+1}:")
            print(f"    合同名称: {row['合同名称']}")
            print(f"    签约单位: {row['合同中的签约单位']}")
            print(f"    机构类型: {row['签约单位的机构类型']}")
            print(f"    单位类型: {row['合同中的签约单位类型']}")
            print()
        
        # 处理后数据
        processed_records = df_processed[df_processed['合同编号'] == contract_id]
        if len(processed_records) > 0:
            processed_record = processed_records.iloc[0]
            print("处理后数据 (1 条记录):")
            print(f"  合同名称: {processed_record['合同名称']}")
            print(f"  合并后签约单位: {processed_record['合同中的签约单位']}")
            print(f"  合并后机构类型: {processed_record['签约单位的机构类型']}")
            print(f"  单位类型: {processed_record['合同中的签约单位类型']}")
        else:
            print("处理后数据: 未找到对应记录")
        
        print("\n" + "="*80 + "\n")
    
    # 统计信息
    print("=== 处理统计信息 ===")
    print(f"原始记录总数: {len(df_original)}")
    print(f"处理后记录数: {len(df_processed)}")
    print(f"数据压缩率: {((len(df_original) - len(df_processed)) / len(df_original) * 100):.2f}%")
    
    # 分析合并情况
    multi_unit_contracts = 0
    total_units_before = 0
    total_units_after = 0
    
    for contract_id in df_processed['合同编号'].unique():
        original_count = len(df_original[df_original['合同编号'] == contract_id])
        processed_units = str(df_processed[df_processed['合同编号'] == contract_id].iloc[0]['合同中的签约单位'])
        
        total_units_before += original_count
        
        if ';' in processed_units:
            multi_unit_contracts += 1
            total_units_after += len(processed_units.split(';'))
        else:
            total_units_after += 1
    
    print(f"\n包含多个签约单位的合同数: {multi_unit_contracts}")
    print(f"平均每个合同的签约单位数 (处理前): {total_units_before / len(df_processed):.2f}")
    print(f"平均每个合同的签约单位数 (处理后): {total_units_after / len(df_processed):.2f}")

if __name__ == "__main__":
    show_processing_examples()
