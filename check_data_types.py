#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据类型和合同编号匹配问题
"""

import pandas as pd

def check_data_types():
    """检查数据类型"""
    
    # 读取数据
    df_original = pd.read_excel("boss3合同签约单位清单20250722.xlsx", header=1)
    df_processed = pd.read_excel("boss3合同签约单位清单20250722_processed_20250723_184659.xlsx")
    
    print("=== 数据类型检查 ===")
    print(f"原始数据合同编号类型: {df_original['合同编号'].dtype}")
    print(f"处理后数据合同编号类型: {df_processed['合同编号'].dtype}")
    
    print(f"\n原始数据合同编号示例: {df_original['合同编号'].head().tolist()}")
    print(f"处理后数据合同编号示例: {df_processed['合同编号'].head().tolist()}")
    
    # 检查重复最多的合同编号
    contract_counts = df_original['合同编号'].value_counts()
    top_contract = contract_counts.index[0]
    
    print(f"\n重复最多的合同编号: {top_contract} (类型: {type(top_contract)})")
    
    # 在处理后数据中查找
    matches = df_processed[df_processed['合同编号'] == top_contract]
    print(f"在处理后数据中找到的匹配记录数: {len(matches)}")
    
    if len(matches) > 0:
        print("找到的记录:")
        print(matches[['合同编号', '合同名称', '合同中的签约单位']].iloc[0])
    else:
        print("未找到匹配记录，尝试字符串匹配...")
        str_matches = df_processed[df_processed['合同编号'].astype(str) == str(top_contract)]
        print(f"字符串匹配结果: {len(str_matches)} 条记录")
        
        if len(str_matches) > 0:
            print("字符串匹配找到的记录:")
            print(str_matches[['合同编号', '合同名称', '合同中的签约单位']].iloc[0])
    
    # 检查合同编号的唯一值数量
    print(f"\n原始数据唯一合同编号数: {df_original['合同编号'].nunique()}")
    print(f"处理后数据唯一合同编号数: {df_processed['合同编号'].nunique()}")
    
    # 检查是否有合同编号丢失
    original_contracts = set(df_original['合同编号'].astype(str))
    processed_contracts = set(df_processed['合同编号'].astype(str))
    
    missing = original_contracts - processed_contracts
    extra = processed_contracts - original_contracts
    
    print(f"\n丢失的合同编号数: {len(missing)}")
    print(f"多出的合同编号数: {len(extra)}")
    
    if missing:
        print(f"丢失的合同编号示例: {list(missing)[:5]}")
    if extra:
        print(f"多出的合同编号示例: {list(extra)[:5]}")

if __name__ == "__main__":
    check_data_types()
