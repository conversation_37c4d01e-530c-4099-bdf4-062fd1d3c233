#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
合同签约单位数据处理脚本
功能：按合同编号去重，合并签约单位信息
作者：AI助手
日期：2025-07-23
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime
import os
import sys

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('data_processing.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

class ContractDataProcessor:
    """合同数据处理器"""
    
    def __init__(self, input_file):
        """
        初始化处理器
        
        Args:
            input_file (str): 输入Excel文件路径
        """
        self.input_file = input_file
        self.df = None
        self.processed_df = None
        self.stats = {}
        
    def load_data(self):
        """加载Excel数据"""
        try:
            logger.info(f"正在加载文件: {self.input_file}")

            # 先尝试正常读取
            df_temp = pd.read_excel(self.input_file)
            logger.info(f"初始加载: {len(df_temp)} 行，{len(df_temp.columns)} 列")
            logger.info(f"初始列名: {list(df_temp.columns)}")

            # 检查是否第一行是列名
            if 'Unnamed' in str(df_temp.columns[0]):
                logger.info("检测到列名可能在第一行数据中，重新读取...")
                # 使用第一行作为列名
                self.df = pd.read_excel(self.input_file, header=0)
                # 如果还是Unnamed，尝试使用第二行
                if 'Unnamed' in str(self.df.columns[0]):
                    logger.info("尝试使用第二行作为列名...")
                    self.df = pd.read_excel(self.input_file, header=1)
            else:
                self.df = df_temp

            logger.info(f"最终数据加载成功，共 {len(self.df)} 行，{len(self.df.columns)} 列")
            logger.info(f"最终列名: {list(self.df.columns)}")

            # 显示前几行数据以了解结构
            logger.info("数据预览:")
            logger.info(f"\n{self.df.head()}")

            # 统计原始数据
            self.stats['original_rows'] = len(self.df)
            self.stats['original_columns'] = len(self.df.columns)

            return True

        except Exception as e:
            logger.error(f"加载数据失败: {str(e)}")
            return False
    
    def analyze_data(self):
        """分析数据结构和质量"""
        if self.df is None:
            logger.error("数据未加载，请先调用 load_data()")
            return False
            
        logger.info("=== 数据分析 ===")
        
        # 检查关键字段
        key_columns = ['合同编号', '合同中的签约单位', '签约单位的机构类型']
        missing_columns = [col for col in key_columns if col not in self.df.columns]
        
        if missing_columns:
            logger.error(f"缺少关键字段: {missing_columns}")
            logger.info(f"实际字段: {list(self.df.columns)}")
            return False
        
        # 统计合同编号重复情况
        contract_counts = self.df['合同编号'].value_counts()
        duplicated_contracts = contract_counts[contract_counts > 1]
        
        logger.info(f"总合同数量: {len(contract_counts)}")
        logger.info(f"有重复记录的合同数量: {len(duplicated_contracts)}")
        logger.info(f"重复记录总数: {duplicated_contracts.sum()}")
        
        # 显示重复最多的合同
        if len(duplicated_contracts) > 0:
            logger.info("重复次数最多的合同:")
            logger.info(f"\n{duplicated_contracts.head(10)}")
        
        # 检查空值情况
        logger.info("空值统计:")
        null_stats = self.df.isnull().sum()
        logger.info(f"\n{null_stats[null_stats > 0]}")
        
        self.stats['unique_contracts'] = len(contract_counts)
        self.stats['duplicated_contracts'] = len(duplicated_contracts)
        self.stats['total_duplicates'] = duplicated_contracts.sum() if len(duplicated_contracts) > 0 else 0
        
        return True
    
    def clean_data(self):
        """数据清洗"""
        logger.info("=== 开始数据清洗 ===")
        
        original_rows = len(self.df)
        
        # 去除完全重复的行
        self.df = self.df.drop_duplicates()
        after_dedup = len(self.df)
        
        if original_rows != after_dedup:
            logger.info(f"去除完全重复行: {original_rows - after_dedup} 行")
        
        # 清理合同编号字段（去除前后空格）
        self.df['合同编号'] = self.df['合同编号'].astype(str).str.strip()
        
        # 清理签约单位字段
        self.df['合同中的签约单位'] = self.df['合同中的签约单位'].astype(str).str.strip()
        self.df['签约单位的机构类型'] = self.df['签约单位的机构类型'].astype(str).str.strip()
        
        # 处理空值
        self.df['合同中的签约单位'] = self.df['合同中的签约单位'].replace(['nan', 'NaN', ''], np.nan)
        self.df['签约单位的机构类型'] = self.df['签约单位的机构类型'].replace(['nan', 'NaN', ''], np.nan)
        
        logger.info("数据清洗完成")
        return True
    
    def process_duplicates(self):
        """处理重复合同编号，合并签约单位信息"""
        logger.info("=== 开始处理重复数据 ===")
        
        if self.df is None:
            logger.error("数据未加载")
            return False
        
        # 按合同编号分组处理
        def merge_contract_info(group):
            """合并同一合同编号下的所有签约单位信息"""
            # 取第一行作为基础记录
            base_record = group.iloc[0].copy()
            
            # 收集所有签约单位和机构类型
            signing_units = []
            org_types = []
            
            for _, row in group.iterrows():
                unit = row['合同中的签约单位']
                org_type = row['签约单位的机构类型']
                
                # 只添加非空值
                if pd.notna(unit) and unit.strip():
                    signing_units.append(unit.strip())
                if pd.notna(org_type) and org_type.strip():
                    org_types.append(org_type.strip())
            
            # 去重并保持顺序
            unique_units = []
            unique_types = []
            seen_units = set()
            
            for i, unit in enumerate(signing_units):
                if unit not in seen_units:
                    unique_units.append(unit)
                    seen_units.add(unit)
                    # 对应的机构类型
                    if i < len(org_types):
                        unique_types.append(org_types[i])
                    else:
                        unique_types.append('')
            
            # 合并字段，使用分号分隔
            base_record['合同中的签约单位'] = '; '.join(unique_units) if unique_units else ''
            base_record['签约单位的机构类型'] = '; '.join(unique_types) if unique_types else ''
            
            return base_record
        
        # 按合同编号分组并合并
        logger.info("正在按合同编号分组合并数据...")
        self.processed_df = self.df.groupby('合同编号').apply(merge_contract_info).reset_index(drop=True)
        
        # 统计处理结果
        self.stats['processed_rows'] = len(self.processed_df)
        self.stats['reduction_count'] = self.stats['original_rows'] - self.stats['processed_rows']
        self.stats['reduction_rate'] = (self.stats['reduction_count'] / self.stats['original_rows']) * 100
        
        logger.info(f"处理完成:")
        logger.info(f"  原始记录数: {self.stats['original_rows']}")
        logger.info(f"  处理后记录数: {self.stats['processed_rows']}")
        logger.info(f"  减少记录数: {self.stats['reduction_count']}")
        logger.info(f"  压缩率: {self.stats['reduction_rate']:.2f}%")
        
        return True
    
    def save_result(self, output_file=None):
        """保存处理结果"""
        if self.processed_df is None:
            logger.error("没有处理后的数据可保存")
            return False
        
        if output_file is None:
            # 生成输出文件名
            base_name = os.path.splitext(self.input_file)[0]
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"{base_name}_processed_{timestamp}.xlsx"
        
        try:
            logger.info(f"正在保存结果到: {output_file}")
            self.processed_df.to_excel(output_file, index=False)
            logger.info("文件保存成功")
            
            self.stats['output_file'] = output_file
            return True
            
        except Exception as e:
            logger.error(f"保存文件失败: {str(e)}")
            return False
    
    def generate_report(self):
        """生成处理报告"""
        logger.info("=== 处理报告 ===")
        
        report = f"""
数据处理完成报告
================
处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
输入文件: {self.input_file}
输出文件: {self.stats.get('output_file', '未保存')}

数据统计:
- 原始记录数: {self.stats['original_rows']}
- 处理后记录数: {self.stats['processed_rows']}
- 减少记录数: {self.stats['reduction_count']}
- 数据压缩率: {self.stats['reduction_rate']:.2f}%
- 唯一合同数: {self.stats['unique_contracts']}
- 有重复的合同数: {self.stats['duplicated_contracts']}

处理说明:
1. 按合同编号进行去重处理
2. 相同合同编号的记录合并为一条
3. 签约单位信息用分号(;)分隔
4. 机构类型信息与签约单位一一对应
5. 保留了所有其他字段的原始信息
        """
        
        logger.info(report)
        
        # 保存报告到文件
        report_file = f"processing_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        logger.info(f"处理报告已保存到: {report_file}")
        
        return report

def main():
    """主函数"""
    input_file = "boss3合同签约单位清单20250722.xlsx"
    
    if not os.path.exists(input_file):
        logger.error(f"输入文件不存在: {input_file}")
        return False
    
    # 创建处理器实例
    processor = ContractDataProcessor(input_file)
    
    # 执行处理流程
    try:
        # 1. 加载数据
        if not processor.load_data():
            return False
        
        # 2. 分析数据
        if not processor.analyze_data():
            return False
        
        # 3. 清洗数据
        if not processor.clean_data():
            return False
        
        # 4. 处理重复数据
        if not processor.process_duplicates():
            return False
        
        # 5. 保存结果
        if not processor.save_result():
            return False
        
        # 6. 生成报告
        processor.generate_report()
        
        logger.info("所有处理步骤完成！")
        return True
        
    except Exception as e:
        logger.error(f"处理过程中发生错误: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
